/**
 * 动画降级和跳过机制
 * 为不兼容的浏览器提供动画降级方案，确保核心功能可用
 */

import { getEarlyCompatibilityResult } from './earlyCompatibilityCheck.js';

// 动画降级配置
const ANIMATION_FALLBACK_CONFIG = {
  // 禁用的动画类型
  disabledAnimations: [
    'transform',
    'transition',
    'animation',
    'keyframes'
  ],
  
  // 降级的CSS属性
  fallbackProperties: {
    'transform': 'position',
    'transition': 'none',
    'animation': 'none',
    'opacity': 'display'
  },
  
  // 快速加载模式的超时时间
  fastLoadTimeout: 500,
  
  // 动画检测的关键特性
  requiredFeatures: [
    'cssTransforms',
    'cssTransitions', 
    'cssAnimations'
  ]
};

/**
 * 检查是否应该跳过动画
 * @returns {boolean} 是否跳过动画
 */
export const shouldSkipAnimations = () => {
  try {
    // 检查全局标记
    if (window.__SKIP_ANIMATIONS__) {
      return true;
    }
    
    // 检查DOM属性
    if (document.documentElement.hasAttribute('data-skip-animations')) {
      return true;
    }
    
    // 检查早期兼容性检测结果
    const earlyResult = getEarlyCompatibilityResult();
    if (earlyResult && earlyResult.shouldSkipAnimations) {
      return true;
    }
    
    // 检查用户偏好设置
    if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.warn('[AnimationFallback] 检查动画跳过状态失败:', error);
    return false;
  }
};

/**
 * 应用动画降级样式
 */
export const applyAnimationFallback = () => {
  try {
    if (!shouldSkipAnimations()) {
      return;
    }
    
    console.log('[AnimationFallback] 应用动画降级样式');
    
    // 创建或更新降级样式
    let fallbackStyle = document.getElementById('animation-fallback-styles');
    if (!fallbackStyle) {
      fallbackStyle = document.createElement('style');
      fallbackStyle.id = 'animation-fallback-styles';
      document.head.appendChild(fallbackStyle);
    }
    
    // 生成降级CSS规则
    const fallbackCSS = `
      /* 全局动画禁用 */
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-delay: 0ms !important;
        transition-duration: 0.01ms !important;
        transition-delay: 0ms !important;
        scroll-behavior: auto !important;
      }
      
      /* 特定组件的降级样式 */
      .pixel-loader-animation,
      .loading-animation,
      .fade-in,
      .slide-up,
      .slide-down {
        animation: none !important;
        transition: none !important;
        opacity: 1 !important;
        transform: none !important;
      }
      
      /* 进度条简化 */
      .progress-bar {
        transition: width 0.1s linear !important;
      }
      
      /* 渐变效果简化 */
      .bg-gradient-to-r,
      .bg-gradient-to-br,
      .bg-gradient-radial {
        background: #3b82f6 !important;
      }
      
      /* 阴影效果移除 */
      .shadow-lg,
      .shadow-md,
      .shadow-glow {
        box-shadow: none !important;
      }
      
      /* 变换效果移除 */
      .transform,
      .scale-100,
      .scale-75,
      .translate-x-0,
      .translate-y-0 {
        transform: none !important;
      }
      
      /* 悬停效果简化 */
      .hover\\:scale-105:hover,
      .hover\\:shadow-lg:hover,
      .hover\\:transform:hover {
        transform: none !important;
        box-shadow: none !important;
      }
    `;
    
    fallbackStyle.textContent = fallbackCSS;
    
    // 设置DOM属性标记
    document.documentElement.setAttribute('data-animation-fallback', 'true');
    document.documentElement.classList.add('animation-fallback');
    
  } catch (error) {
    console.error('[AnimationFallback] 应用动画降级样式失败:', error);
  }
};

/**
 * 移除动画降级样式
 */
export const removeAnimationFallback = () => {
  try {
    console.log('[AnimationFallback] 移除动画降级样式');
    
    // 移除降级样式
    const fallbackStyle = document.getElementById('animation-fallback-styles');
    if (fallbackStyle) {
      fallbackStyle.remove();
    }
    
    // 移除DOM标记
    document.documentElement.removeAttribute('data-animation-fallback');
    document.documentElement.removeAttribute('data-skip-animations');
    document.documentElement.classList.remove('animation-fallback', 'skip-animations');
    
    // 清除全局标记
    delete window.__SKIP_ANIMATIONS__;
    
  } catch (error) {
    console.error('[AnimationFallback] 移除动画降级样式失败:', error);
  }
};

/**
 * 创建快速加载组件
 * @param {string} message 加载消息
 * @param {number} progress 进度百分比
 * @returns {HTMLElement} 快速加载元素
 */
export const createFastLoader = (message = '正在加载...', progress = 0) => {
  try {
    const loader = document.createElement('div');
    loader.id = 'fast-loader';
    loader.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: #000;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    loader.innerHTML = `
      <div style="text-align: center;">
        <h1 style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem;">YNNX AI 开发平台</h1>
        <p style="font-size: 1rem; color: #ccc; margin-bottom: 2rem;">${message}</p>
        <div style="width: 300px; height: 4px; background: #333; border-radius: 2px; overflow: hidden;">
          <div id="fast-loader-progress" style="height: 100%; background: #3b82f6; width: ${progress}%; transition: width 0.1s linear;"></div>
        </div>
        <p style="font-size: 0.875rem; color: #888; margin-top: 1rem;">兼容模式 - 已优化加载速度</p>
      </div>
    `;
    
    return loader;
  } catch (error) {
    console.error('[AnimationFallback] 创建快速加载组件失败:', error);
    return null;
  }
};

/**
 * 更新快速加载进度
 * @param {number} progress 进度百分比
 * @param {string} message 加载消息
 */
export const updateFastLoader = (progress, message) => {
  try {
    const loader = document.getElementById('fast-loader');
    const progressBar = document.getElementById('fast-loader-progress');
    
    if (progressBar) {
      progressBar.style.width = `${progress}%`;
    }
    
    if (message && loader) {
      const messageElement = loader.querySelector('p');
      if (messageElement) {
        messageElement.textContent = message;
      }
    }
  } catch (error) {
    console.warn('[AnimationFallback] 更新快速加载进度失败:', error);
  }
};

/**
 * 移除快速加载组件
 */
export const removeFastLoader = () => {
  try {
    const loader = document.getElementById('fast-loader');
    if (loader) {
      loader.remove();
    }
  } catch (error) {
    console.warn('[AnimationFallback] 移除快速加载组件失败:', error);
  }
};

/**
 * 初始化动画降级机制
 */
export const initializeAnimationFallback = () => {
  try {
    if (shouldSkipAnimations()) {
      console.log('[AnimationFallback] 初始化动画降级机制');
      applyAnimationFallback();
      
      // 监听DOM变化，确保新添加的元素也应用降级样式
      if (window.MutationObserver) {
        const observer = new MutationObserver(() => {
          if (shouldSkipAnimations()) {
            applyAnimationFallback();
          }
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }
  } catch (error) {
    console.error('[AnimationFallback] 初始化动画降级机制失败:', error);
  }
};

// 自动初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeAnimationFallback);
} else {
  initializeAnimationFallback();
}
