/**
 * 兼容性检测初始化脚本
 * 在页面加载的最早阶段执行，确保在任何动画或复杂逻辑之前完成检测
 */

import {
  performEarlyCompatibilityCheck,
  setEarlyCompatibilityResult
} from './earlyCompatibilityCheck.js';
import { applyAnimationFallback } from './animationFallback.js';

// 全局标记，防止重复执行
let compatibilityInitialized = false;

/**
 * 显示兼容性警告 - 内联样式确保在任何CSS加载前都能显示
 * @param {Object} result 兼容性检测结果
 */
const showCompatibilityWarning = (result) => {
  try {
    // 创建警告容器
    const warningDiv = document.createElement('div');
    warningDiv.id = 'early-compatibility-warning';
    warningDiv.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 999999;
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      color: white;
      padding: 12px 20px;
      text-align: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      border-bottom: 3px solid #991b1b;
    `;

    // 生成警告内容
    const browserName = result.browser.name || '当前浏览器';
    const browserVersion = result.browser.version || '未知版本';
    
    let warningMessage = `⚠️ 浏览器兼容性警告：${browserName} ${browserVersion}`;
    
    if (!result.isBasicCompatible) {
      warningMessage += ' 可能无法正常运行本应用的所有功能。';
    } else if (!result.isAnimationCompatible) {
      warningMessage += ' 将跳过动画效果以确保正常使用。';
    }
    
    warningMessage += ' 建议升级到最新版本的 Chrome、Firefox 或 Safari 浏览器。';

    // 创建关闭按钮
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.cssText = `
      position: absolute;
      top: 8px;
      right: 12px;
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      font-weight: bold;
      cursor: pointer;
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;
    `;
    
    closeButton.onmouseover = () => {
      closeButton.style.backgroundColor = 'rgba(255,255,255,0.2)';
    };
    
    closeButton.onmouseout = () => {
      closeButton.style.backgroundColor = 'transparent';
    };
    
    closeButton.onclick = () => {
      warningDiv.remove();
    };

    // 设置警告内容
    warningDiv.innerHTML = warningMessage;
    warningDiv.appendChild(closeButton);

    // 添加到页面
    document.body.appendChild(warningDiv);

    // 10秒后自动隐藏（如果用户没有手动关闭）
    setTimeout(() => {
      if (warningDiv.parentNode) {
        warningDiv.style.transition = 'opacity 0.5s ease-out';
        warningDiv.style.opacity = '0';
        setTimeout(() => {
          if (warningDiv.parentNode) {
            warningDiv.remove();
          }
        }, 500);
      }
    }, 10000);

  } catch (error) {
    console.error('[CompatibilityInit] 显示兼容性警告失败:', error);
  }
};

/**
 * 设置动画跳过标记
 * @param {boolean} shouldSkip 是否跳过动画
 */
const setAnimationSkipFlag = (shouldSkip) => {
  try {
    if (shouldSkip) {
      // 在document上设置属性，供CSS和JS检测
      document.documentElement.setAttribute('data-skip-animations', 'true');
      document.documentElement.classList.add('skip-animations');
      
      // 设置全局变量
      window.__SKIP_ANIMATIONS__ = true;
      
      // 添加CSS规则禁用动画
      const style = document.createElement('style');
      style.id = 'animation-skip-styles';
      style.textContent = `
        .skip-animations *,
        .skip-animations *::before,
        .skip-animations *::after {
          animation-duration: 0.01ms !important;
          animation-delay: 0ms !important;
          transition-duration: 0.01ms !important;
          transition-delay: 0ms !important;
        }
        
        /* 特别针对PixelLoader的动画跳过 */
        .skip-animations .pixel-loader-animation {
          display: none !important;
        }
        
        .skip-animations .loading-animation {
          display: none !important;
        }
      `;
      document.head.appendChild(style);
      
      console.log('[CompatibilityInit] 已设置动画跳过模式');
    }
  } catch (error) {
    console.error('[CompatibilityInit] 设置动画跳过标记失败:', error);
  }
};

/**
 * 初始化兼容性检测
 * 在页面加载最早阶段执行
 */
export const initializeCompatibilityCheck = () => {
  // 防止重复执行
  if (compatibilityInitialized) {
    console.log('[CompatibilityInit] 兼容性检测已初始化，跳过重复执行');
    return;
  }

  try {
    console.log('[CompatibilityInit] 开始早期兼容性检测...');
    
    // 标记为已初始化
    compatibilityInitialized = true;
    
    // 执行早期兼容性检测
    const result = performEarlyCompatibilityCheck();
    
    // 保存检测结果
    setEarlyCompatibilityResult(result);
    
    // 根据检测结果采取相应措施
    if (result.shouldSkipAnimations) {
      setAnimationSkipFlag(true);
      applyAnimationFallback(); // 应用动画降级样式
      console.log('[CompatibilityInit] 检测到动画不兼容，已启用动画跳过模式和降级样式');
    }
    
    if (result.shouldShowWarning) {
      showCompatibilityWarning(result);
      console.log('[CompatibilityInit] 显示兼容性警告');
    }
    
    // 记录性能标记
    if (performance.mark) {
      performance.mark('early-compatibility-check-complete');
    }
    
    console.log('[CompatibilityInit] 早期兼容性检测完成:', result);
    
    return result;
    
  } catch (error) {
    console.error('[CompatibilityInit] 兼容性检测初始化失败:', error);
    
    // 在错误情况下，采用保守策略
    setAnimationSkipFlag(true);
    
    return {
      error: error.message,
      shouldSkipAnimations: true,
      shouldShowWarning: false,
      isBasicCompatible: true
    };
  }
};

/**
 * DOM就绪时自动执行兼容性检测
 */
const autoInitialize = () => {
  if (document.readyState === 'loading') {
    // 如果DOM还在加载，等待DOMContentLoaded事件
    document.addEventListener('DOMContentLoaded', initializeCompatibilityCheck, { once: true });
  } else {
    // 如果DOM已经加载完成，立即执行
    initializeCompatibilityCheck();
  }
};

// 立即尝试初始化
autoInitialize();

// 导出初始化函数供手动调用
export default initializeCompatibilityCheck;
