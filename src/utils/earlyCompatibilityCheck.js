/**
 * 早期浏览器兼容性检测
 * 在页面加载最初阶段执行，在任何动画或复杂逻辑之前
 * 为不兼容的浏览器提供快速检测和降级方案
 */

// 最基础的浏览器版本要求 - 用于早期检测
const CRITICAL_BROWSER_VERSIONS = {
  Chrome: 58,    // CSS Grid, ES6基础支持
  Firefox: 52,   // CSS Grid, ES6基础支持
  Safari: 10.1,  // CSS Grid, ES6基础支持
  Edge: 16,      // CSS Grid, ES6基础支持
  Opera: 44,     // 基于Chromium
  Samsung: 6.2,  // 移动端基础支持
  'Internet Explorer': 999, // IE完全不支持
};

// 动画相关的浏览器要求
const ANIMATION_BROWSER_VERSIONS = {
  Chrome: 60,    // 完整的CSS动画和变换支持
  Firefox: 55,   // 完整的CSS动画和变换支持
  Safari: 11,    // 完整的CSS动画和变换支持
  Edge: 17,      // 完整的CSS动画和变换支持
  Opera: 47,     // 基于Chromium
  Samsung: 7,    // 移动端动画支持
};

/**
 * 快速浏览器信息检测 - 轻量级版本
 * @returns {Object} 基础浏览器信息
 */
export const getBasicBrowserInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const browser = {
    name: 'Unknown',
    version: 0,
    isModern: false,
    supportsAnimations: false
  };

  try {
    // Chrome检测
    if (userAgent.includes('chrome') && !userAgent.includes('edge')) {
      const match = userAgent.match(/chrome\/(\d+)/);
      browser.name = 'Chrome';
      browser.version = match ? parseInt(match[1]) : 0;
    }
    // Firefox检测
    else if (userAgent.includes('firefox')) {
      const match = userAgent.match(/firefox\/(\d+)/);
      browser.name = 'Firefox';
      browser.version = match ? parseInt(match[1]) : 0;
    }
    // Safari检测
    else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
      const match = userAgent.match(/version\/(\d+)/);
      browser.name = 'Safari';
      browser.version = match ? parseInt(match[1]) : 0;
    }
    // Edge检测
    else if (userAgent.includes('edge') || userAgent.includes('edg/')) {
      const match = userAgent.match(/(?:edge|edg)\/(\d+)/);
      browser.name = 'Edge';
      browser.version = match ? parseInt(match[1]) : 0;
    }
    // Opera检测
    else if (userAgent.includes('opera') || userAgent.includes('opr/')) {
      const match = userAgent.match(/(?:opera|opr)\/(\d+)/);
      browser.name = 'Opera';
      browser.version = match ? parseInt(match[1]) : 0;
    }
    // IE检测
    else if (userAgent.includes('trident') || userAgent.includes('msie')) {
      browser.name = 'Internet Explorer';
      browser.version = 11; // 假设是IE11
    }
    // 国产浏览器检测
    else if (userAgent.includes('360se')) {
      browser.name = '360SE';
      browser.version = 13; // 默认版本
    }
    else if (userAgent.includes('micromessenger')) {
      browser.name = 'WeChat';
      browser.version = 7; // 默认版本
    }

    // 判断是否为现代浏览器
    const minVersion = CRITICAL_BROWSER_VERSIONS[browser.name];
    browser.isModern = minVersion ? browser.version >= minVersion : false;

    // 判断是否支持动画
    const animationVersion = ANIMATION_BROWSER_VERSIONS[browser.name];
    browser.supportsAnimations = animationVersion ? browser.version >= animationVersion : browser.isModern;

  } catch (error) {
    console.warn('[EarlyCompatibility] 浏览器检测失败:', error);
  }

  return browser;
};

/**
 * 快速特性检测 - 只检测最关键的特性
 * @returns {Object} 关键特性支持情况
 */
export const checkCriticalFeatures = () => {
  const features = {};

  try {
    // ES6基础支持
    features.es6Basic = (
      typeof Promise !== 'undefined' &&
      typeof Symbol !== 'undefined' &&
      Array.prototype.includes
    );

    // CSS基础支持
    const testDiv = document.createElement('div');
    features.cssFlexbox = 'flex' in testDiv.style || 'webkitFlex' in testDiv.style;
    features.cssTransforms = 'transform' in testDiv.style || 'webkitTransform' in testDiv.style;
    features.cssTransitions = 'transition' in testDiv.style || 'webkitTransition' in testDiv.style;

    // 动画支持检测
    features.animationSupport = (
      features.cssTransforms &&
      features.cssTransitions &&
      ('animation' in testDiv.style || 'webkitAnimation' in testDiv.style)
    );

    // 基础API支持
    features.fetchAPI = typeof fetch !== 'undefined';
    features.localStorage = (() => {
      try {
        return typeof localStorage !== 'undefined' && localStorage !== null;
      } catch (e) {
        return false;
      }
    })();

  } catch (error) {
    console.warn('[EarlyCompatibility] 特性检测失败:', error);
    // 在错误情况下返回保守的结果
    return {
      es6Basic: false,
      cssFlexbox: false,
      cssTransforms: false,
      cssTransitions: false,
      animationSupport: false,
      fetchAPI: false,
      localStorage: false
    };
  }

  return features;
};

/**
 * 早期兼容性检测 - 页面加载初期执行
 * @returns {Object} 兼容性检测结果
 */
export const performEarlyCompatibilityCheck = () => {
  try {
    const browser = getBasicBrowserInfo();
    const features = checkCriticalFeatures();

    // 判断基础兼容性
    const isBasicCompatible = (
      browser.isModern &&
      features.es6Basic &&
      features.cssFlexbox &&
      features.fetchAPI &&
      features.localStorage
    );

    // 判断动画兼容性
    const isAnimationCompatible = (
      browser.supportsAnimations &&
      features.animationSupport
    );

    // 生成兼容性报告
    const compatibilityResult = {
      browser,
      features,
      isBasicCompatible,
      isAnimationCompatible,
      shouldSkipAnimations: !isAnimationCompatible,
      shouldShowWarning: !isBasicCompatible,
      timestamp: Date.now()
    };

    // 记录检测结果
    console.log('[EarlyCompatibility] 早期兼容性检测完成:', compatibilityResult);

    return compatibilityResult;

  } catch (error) {
    console.error('[EarlyCompatibility] 早期兼容性检测失败:', error);
    
    // 返回安全的默认值
    return {
      browser: { name: 'Unknown', version: 0, isModern: false, supportsAnimations: false },
      features: {},
      isBasicCompatible: true, // 保守策略：假设兼容
      isAnimationCompatible: false, // 保守策略：跳过动画
      shouldSkipAnimations: true,
      shouldShowWarning: false,
      timestamp: Date.now(),
      error: error.message
    };
  }
};

/**
 * 设置早期兼容性检测结果到全局
 * @param {Object} result 检测结果
 */
export const setEarlyCompatibilityResult = (result) => {
  try {
    // 存储到window对象供其他模块使用
    window.__EARLY_COMPATIBILITY_RESULT__ = result;
    
    // 存储到sessionStorage作为备份
    if (result.features.localStorage) {
      sessionStorage.setItem('early-compatibility-result', JSON.stringify(result));
    }
    
    // 发送自定义事件通知其他组件
    if (typeof CustomEvent !== 'undefined') {
      const event = new CustomEvent('earlyCompatibilityChecked', {
        detail: result
      });
      window.dispatchEvent(event);
    }
  } catch (error) {
    console.warn('[EarlyCompatibility] 设置检测结果失败:', error);
  }
};

/**
 * 获取早期兼容性检测结果
 * @returns {Object|null} 检测结果
 */
export const getEarlyCompatibilityResult = () => {
  try {
    // 优先从window对象获取
    if (window.__EARLY_COMPATIBILITY_RESULT__) {
      return window.__EARLY_COMPATIBILITY_RESULT__;
    }
    
    // 从sessionStorage获取备份
    const stored = sessionStorage.getItem('early-compatibility-result');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.warn('[EarlyCompatibility] 获取检测结果失败:', error);
  }
  
  return null;
};
