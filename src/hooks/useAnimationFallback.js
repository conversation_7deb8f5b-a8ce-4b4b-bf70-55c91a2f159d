/**
 * React Hook for managing animation fallback
 * 管理动画降级状态的React Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  shouldSkipAnimations, 
  applyAnimationFallback, 
  removeAnimationFallback 
} from '../utils/animationFallback.js';
import { getEarlyCompatibilityResult } from '../utils/earlyCompatibilityCheck.js';

/**
 * 动画降级管理Hook
 * @returns {Object} 动画降级状态和控制函数
 */
export const useAnimationFallback = () => {
  const [isAnimationDisabled, setIsAnimationDisabled] = useState(false);
  const [compatibilityResult, setCompatibilityResult] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 检查动画状态
  const checkAnimationStatus = useCallback(() => {
    try {
      const skipAnimations = shouldSkipAnimations();
      const earlyResult = getEarlyCompatibilityResult();
      
      setIsAnimationDisabled(skipAnimations);
      setCompatibilityResult(earlyResult);
      
      return skipAnimations;
    } catch (error) {
      console.warn('[useAnimationFallback] 检查动画状态失败:', error);
      return false;
    }
  }, []);

  // 启用动画降级
  const enableFallback = useCallback(() => {
    try {
      applyAnimationFallback();
      setIsAnimationDisabled(true);
      console.log('[useAnimationFallback] 已启用动画降级');
    } catch (error) {
      console.error('[useAnimationFallback] 启用动画降级失败:', error);
    }
  }, []);

  // 禁用动画降级
  const disableFallback = useCallback(() => {
    try {
      removeAnimationFallback();
      setIsAnimationDisabled(false);
      console.log('[useAnimationFallback] 已禁用动画降级');
    } catch (error) {
      console.error('[useAnimationFallback] 禁用动画降级失败:', error);
    }
  }, []);

  // 切换动画降级状态
  const toggleFallback = useCallback(() => {
    if (isAnimationDisabled) {
      disableFallback();
    } else {
      enableFallback();
    }
  }, [isAnimationDisabled, enableFallback, disableFallback]);

  // 获取动画相关的CSS类名
  const getAnimationClasses = useCallback((normalClasses = '', fallbackClasses = '') => {
    return isAnimationDisabled ? fallbackClasses : normalClasses;
  }, [isAnimationDisabled]);

  // 获取条件渲染的动画组件
  const renderWithFallback = useCallback((animatedComponent, fallbackComponent = null) => {
    return isAnimationDisabled ? fallbackComponent : animatedComponent;
  }, [isAnimationDisabled]);

  // 初始化和监听兼容性状态变化
  useEffect(() => {
    // 初始检查
    checkAnimationStatus();
    setIsInitialized(true);

    // 监听兼容性检测事件
    const handleCompatibilityCheck = (event) => {
      try {
        const result = event.detail;
        setCompatibilityResult(result);
        
        if (result && result.shouldSkipAnimations) {
          enableFallback();
        }
      } catch (error) {
        console.warn('[useAnimationFallback] 处理兼容性检测事件失败:', error);
      }
    };

    // 监听早期兼容性检测事件
    const handleEarlyCompatibilityCheck = (event) => {
      try {
        const result = event.detail;
        setCompatibilityResult(result);
        
        if (result && result.shouldSkipAnimations) {
          enableFallback();
        }
      } catch (error) {
        console.warn('[useAnimationFallback] 处理早期兼容性检测事件失败:', error);
      }
    };

    // 监听用户偏好变化
    const handlePrefersReducedMotion = (e) => {
      if (e.matches) {
        enableFallback();
      }
    };

    // 添加事件监听器
    window.addEventListener('browserCompatibilityChecked', handleCompatibilityCheck);
    window.addEventListener('earlyCompatibilityChecked', handleEarlyCompatibilityCheck);
    
    // 监听用户偏好设置
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      mediaQuery.addListener(handlePrefersReducedMotion);
      
      // 初始检查
      if (mediaQuery.matches) {
        enableFallback();
      }
    }

    // 清理函数
    return () => {
      window.removeEventListener('browserCompatibilityChecked', handleCompatibilityCheck);
      window.removeEventListener('earlyCompatibilityChecked', handleEarlyCompatibilityCheck);
      
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        mediaQuery.removeListener(handlePrefersReducedMotion);
      }
    };
  }, [checkAnimationStatus, enableFallback]);

  return {
    // 状态
    isAnimationDisabled,
    compatibilityResult,
    isInitialized,
    
    // 控制函数
    enableFallback,
    disableFallback,
    toggleFallback,
    checkAnimationStatus,
    
    // 工具函数
    getAnimationClasses,
    renderWithFallback,
    
    // 便捷属性
    shouldUseSimpleUI: isAnimationDisabled,
    isCompatible: compatibilityResult?.isBasicCompatible ?? true,
    supportsAnimations: compatibilityResult?.isAnimationCompatible ?? true
  };
};

/**
 * 简化版Hook，只返回是否禁用动画
 * @returns {boolean} 是否禁用动画
 */
export const useSimpleAnimationCheck = () => {
  const [isDisabled, setIsDisabled] = useState(false);

  useEffect(() => {
    const checkStatus = () => {
      setIsDisabled(shouldSkipAnimations());
    };

    checkStatus();

    // 监听兼容性检测事件
    const handleCompatibilityEvent = () => {
      checkStatus();
    };

    window.addEventListener('browserCompatibilityChecked', handleCompatibilityEvent);
    window.addEventListener('earlyCompatibilityChecked', handleCompatibilityEvent);

    return () => {
      window.removeEventListener('browserCompatibilityChecked', handleCompatibilityEvent);
      window.removeEventListener('earlyCompatibilityChecked', handleCompatibilityEvent);
    };
  }, []);

  return isDisabled;
};

export default useAnimationFallback;
