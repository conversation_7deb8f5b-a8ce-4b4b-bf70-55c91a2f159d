import React from 'react';
import { useSimpleAnimationCheck } from '../hooks/useAnimationFallback';

const HeroSection = () => {
  const isAnimationDisabled = useSimpleAnimationCheck();

  // 像素文字组件 - 支持动画降级
  const PixelText = () => {
    const textPixels = [
      [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
      [0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
      [0,0,0,1,0,1,0,0,0,1,1,0,0,1,0,0,1,1,0,0,1,0,0,0,1,0,1,0,0,0],
      [0,0,0,0,1,0,0,0,0,1,0,1,0,1,0,0,1,0,1,0,1,0,0,0,0,1,0,0,0,0],
      [0,0,0,0,1,0,0,0,0,1,0,0,1,1,0,0,1,0,0,1,1,0,0,0,1,0,1,0,0,0],
      [0,0,0,0,1,0,0,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,0,1,0,0],
      [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
    ];

    return (
      <div 
        className="grid gap-[2px] p-4 mx-auto justify-center"
        style={{
          gridTemplateColumns: `repeat(${textPixels[0].length}, 1fr)`,
          gridTemplateRows: `repeat(${textPixels.length}, 1fr)`,
          width: 'fit-content'
        }}
      >
        {textPixels.map((row, rowIndex) => 
          row.map((pixel, colIndex) => {
            const delay = colIndex * 0.03 + rowIndex * 0.02 + 1;
            const colorIndex = (colIndex + rowIndex) % 6;
            const gradients = [
              'bg-gradient-to-br from-red-400 to-pink-500',
              'bg-gradient-to-br from-orange-400 to-red-500', 
              'bg-gradient-to-br from-yellow-400 to-orange-500',
              'bg-gradient-to-br from-green-400 to-teal-500',
              'bg-gradient-to-br from-cyan-400 to-blue-500',
              'bg-gradient-to-br from-blue-400 to-purple-500'
            ];
            const shadows = [
              'shadow-lg shadow-red-500/50',
              'shadow-lg shadow-orange-500/50',
              'shadow-lg shadow-yellow-500/50', 
              'shadow-lg shadow-green-500/50',
              'shadow-lg shadow-cyan-500/50',
              'shadow-lg shadow-purple-500/50'
            ];
            return (
              <div
                key={`${rowIndex}-${colIndex}`}
                className={`${isAnimationDisabled ? '' : 'animate-scale-in'} ${
                  pixel === 1
                    ? `${gradients[colorIndex]} ${isAnimationDisabled ? '' : shadows[colorIndex]}`
                    : 'bg-gray-900/20'
                } rounded-sm`}
                style={{
                  width: 'clamp(12px, 2.5vw, 20px)',
                  height: 'clamp(12px, 2.5vw, 20px)',
                  ...(isAnimationDisabled ? {} : {
                    animationDelay: `${delay}s`,
                    animationFillMode: 'both'
                  })
                }}
              />
            );
          })
        )}
      </div>
    );
  };

  return (
    <section id="home" className="relative min-h-screen overflow-hidden bg-black">
      {/* 背景动画 - 支持降级 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-cyan-600/20"></div>
        {!isAnimationDisabled && (
          <>
            <div
              className="absolute inset-0 animate-pulse"
              style={{
                background: 'radial-gradient(circle at 20% 50%, rgba(6, 182, 212, 0.3) 0%, transparent 50%)',
                animationDuration: '8s'
              }}
            />
            <div
              className="absolute inset-0 animate-pulse"
              style={{
                background: 'radial-gradient(circle at 80% 50%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)',
                animationDuration: '8s',
                animationDelay: '2s'
              }}
            />
          </>
        )}
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

      {/* 主要内容 */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen pt-24 sm:pt-28 pb-16 px-4 animate-fade-in-up">
        {/* 像素艺术LOGO */}
        <div className="mb-8 mt-4 flex justify-center w-full">
          <PixelText />
        </div>

        {/* 主标题 */}
        <div className="text-center mb-8 animate-fade-in-up" style={{ animationDelay: '0.5s', animationFillMode: 'both' }}>
          <h1 className="text-6xl md:text-8xl font-black bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent mb-4 leading-tight">
            AI 开发平台
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-6">
            云南农信智能开发助手
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-lg text-cyan-400">
            <span className="flex items-center gap-2">
              <i className="fas fa-robot text-green-400"></i>
              智能问答
            </span>
            <span className="flex items-center gap-2">
              <i className="fas fa-code text-blue-400"></i>
              代码生成
            </span>
            <span className="flex items-center gap-2">
              <i className="fas fa-key text-purple-400"></i>
              API管理
            </span>
          </div>
        </div>

        {/* 平台核心价值介绍 */}
        <div className="max-w-4xl mx-auto text-center mb-8 animate-fade-in-up" style={{ animationDelay: '1.2s', animationFillMode: 'both' }}>
          <p className="text-xl md:text-2xl text-gray-300 mb-6 leading-relaxed">
            <i className="fas fa-rocket text-cyan-400 mr-2"></i>
            <span className="text-cyan-400 font-semibold">一站式AI开发解决方案</span>
            <i className="fas fa-rocket text-cyan-400 ml-2"></i>
          </p>
          <p className="text-lg text-gray-400 mb-8 leading-relaxed">
            专为云南农信开发者打造的智能开发平台，集成多种AI模型，提供智能问答、API管理、密钥管理等核心功能
          </p>
          
          {/* 核心功能快速预览 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 max-w-5xl mx-auto">
            <div className="bg-gray-800/30 backdrop-blur-sm border border-cyan-500/20 rounded-xl p-6 hover:border-cyan-500/50 transition-all duration-300 animate-scale-in" style={{ animationDelay: '1.5s', animationFillMode: 'both' }}>
              <div className="text-cyan-400 text-2xl mb-3">
                <i className="fas fa-robot"></i>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">AI智能助手</h3>
              <p className="text-gray-400 text-sm">支持代码生成、问题解答、技术咨询，让编程更高效</p>
            </div>
            
            <div className="bg-gray-800/30 backdrop-blur-sm border border-blue-500/20 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 animate-scale-in" style={{ animationDelay: '1.7s', animationFillMode: 'both' }}>
              <div className="text-blue-400 text-2xl mb-3">
                <i className="fas fa-key"></i>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">API密钥管理</h3>
              <p className="text-gray-400 text-sm">安全便捷的API密钥管理，支持多种AI模型配置</p>
            </div>
            
            <div className="bg-gray-800/30 backdrop-blur-sm border border-purple-500/20 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 animate-scale-in" style={{ animationDelay: '1.9s', animationFillMode: 'both' }}>
              <div className="text-purple-400 text-2xl mb-3">
                <i className="fas fa-tools"></i>
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">开发工具集成</h3>
              <p className="text-gray-400 text-sm">VS Code、JetBrains 等IDE插件，无缝集成开发环境</p>
            </div>
          </div>
        </div>

        {/* CTA按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up" style={{ animationDelay: '2.1s', animationFillMode: 'both' }}>
          <a
            href="#ai-assistant"
            className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-cyan-500/25"
          >
            <i className="fas fa-rocket mr-2"></i>开始体验
          </a>
          <a
            href="#downloads"
            className="border-2 border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-black font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:scale-105"
          >
            <i className="fas fa-download mr-2"></i>下载工具
          </a>
        </div>
      </div>
    </section>
  );
};

export default HeroSection; 