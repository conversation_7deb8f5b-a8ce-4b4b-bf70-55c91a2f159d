/**
 * 兼容性帮助模态框
 * 提供详细的浏览器升级指导和兼容性说明
 */

import React, { useState } from 'react';
import { getRecommendedBrowsers } from '../utils/browserCompatibility';
import { getEarlyCompatibilityResult } from '../utils/earlyCompatibilityCheck';

const CompatibilityHelpModal = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('upgrade'); // 'upgrade', 'compatibility', 'troubleshooting'
  
  const compatibilityResult = getEarlyCompatibilityResult();
  const recommendedBrowsers = getRecommendedBrowsers();

  if (!isOpen) return null;

  // 获取当前浏览器的具体问题
  const getCurrentIssues = () => {
    if (!compatibilityResult) return [];
    
    const issues = [];
    
    if (!compatibilityResult.isBasicCompatible) {
      issues.push({
        type: 'critical',
        title: '基础功能不兼容',
        description: '您的浏览器不支持本应用所需的基础功能，可能导致应用无法正常运行。',
        solution: '请升级到支持现代Web标准的浏览器版本。'
      });
    }
    
    if (!compatibilityResult.isAnimationCompatible) {
      issues.push({
        type: 'warning',
        title: '动画效果受限',
        description: '您的浏览器不完全支持CSS动画和变换效果。',
        solution: '应用已自动启用兼容模式，核心功能仍可正常使用。'
      });
    }
    
    if (compatibilityResult.features) {
      const missingFeatures = [];
      if (!compatibilityResult.features.es6Basic) missingFeatures.push('ES6 JavaScript');
      if (!compatibilityResult.features.cssFlexbox) missingFeatures.push('CSS Flexbox');
      if (!compatibilityResult.features.cssTransforms) missingFeatures.push('CSS 变换');
      if (!compatibilityResult.features.fetchAPI) missingFeatures.push('Fetch API');
      
      if (missingFeatures.length > 0) {
        issues.push({
          type: 'info',
          title: '部分功能受限',
          description: `缺少以下功能支持：${missingFeatures.join('、')}`,
          solution: '升级浏览器可以获得完整的功能体验。'
        });
      }
    }
    
    return issues;
  };

  // 升级指导标签页
  const UpgradeTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">推荐浏览器</h3>
        <p className="text-gray-600 mb-4">
          以下浏览器经过测试，能够完美支持本应用的所有功能：
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {recommendedBrowsers.map((browser, index) => (
            <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center gap-3 mb-2">
                <span className="text-2xl">{browser.icon}</span>
                <div>
                  <h4 className="font-medium">{browser.name}</h4>
                  <p className="text-sm text-gray-500">推荐版本 {browser.minVersion}+</p>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-3">{browser.description}</p>
              <a
                href={browser.downloadUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
              >
                <span>下载 {browser.name}</span>
                <span>↗</span>
              </a>
            </div>
          ))}
        </div>
      </div>
      
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 className="font-medium text-yellow-800 mb-2">💡 升级提示</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• 升级前请备份重要的书签和密码</li>
          <li>• 建议选择自动更新功能，保持浏览器始终为最新版本</li>
          <li>• 如果是企业环境，请联系IT部门协助升级</li>
        </ul>
      </div>
    </div>
  );

  // 兼容性说明标签页
  const CompatibilityTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">当前浏览器状态</h3>
        {compatibilityResult && (
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">浏览器：</span>
                <span>{compatibilityResult.browser?.name || '未知'}</span>
              </div>
              <div>
                <span className="font-medium">版本：</span>
                <span>{compatibilityResult.browser?.version || '未知'}</span>
              </div>
              <div>
                <span className="font-medium">平台：</span>
                <span>{compatibilityResult.browser?.platform || '未知'}</span>
              </div>
              <div>
                <span className="font-medium">兼容性：</span>
                <span className={compatibilityResult.isBasicCompatible ? 'text-green-600' : 'text-red-600'}>
                  {compatibilityResult.isBasicCompatible ? '兼容' : '不兼容'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-3">检测到的问题</h3>
        <div className="space-y-3">
          {getCurrentIssues().map((issue, index) => (
            <div key={index} className={`border rounded-lg p-4 ${
              issue.type === 'critical' ? 'border-red-200 bg-red-50' :
              issue.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
              'border-blue-200 bg-blue-50'
            }`}>
              <h4 className={`font-medium mb-2 ${
                issue.type === 'critical' ? 'text-red-800' :
                issue.type === 'warning' ? 'text-yellow-800' :
                'text-blue-800'
              }`}>
                {issue.title}
              </h4>
              <p className={`text-sm mb-2 ${
                issue.type === 'critical' ? 'text-red-700' :
                issue.type === 'warning' ? 'text-yellow-700' :
                'text-blue-700'
              }`}>
                {issue.description}
              </p>
              <p className={`text-sm font-medium ${
                issue.type === 'critical' ? 'text-red-800' :
                issue.type === 'warning' ? 'text-yellow-800' :
                'text-blue-800'
              }`}>
                解决方案：{issue.solution}
              </p>
            </div>
          ))}
          
          {getCurrentIssues().length === 0 && (
            <div className="border border-green-200 bg-green-50 rounded-lg p-4">
              <p className="text-green-800">✅ 您的浏览器完全兼容本应用！</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // 故障排除标签页
  const TroubleshootingTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-3">常见问题解决</h3>
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">页面加载缓慢或卡住</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 清除浏览器缓存和Cookie</li>
              <li>• 禁用浏览器扩展程序</li>
              <li>• 检查网络连接</li>
              <li>• 尝试使用隐私/无痕模式</li>
            </ul>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">功能无法正常使用</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 确保JavaScript已启用</li>
              <li>• 检查是否阻止了弹出窗口</li>
              <li>• 更新浏览器到最新版本</li>
              <li>• 尝试刷新页面（Ctrl+F5）</li>
            </ul>
          </div>
          
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-2">显示效果异常</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 检查浏览器缩放级别（建议100%）</li>
              <li>• 确保CSS和字体文件正常加载</li>
              <li>• 尝试切换到兼容模式</li>
              <li>• 检查显示器分辨率设置</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">🔧 仍然有问题？</h4>
        <p className="text-sm text-blue-700 mb-2">
          如果以上方法都无法解决问题，请尝试以下步骤：
        </p>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 联系技术支持团队</li>
          <li>• 提供浏览器版本和错误信息</li>
          <li>• 描述具体的操作步骤和问题现象</li>
        </ul>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">浏览器兼容性帮助</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="关闭"
          >
            <span className="text-2xl">×</span>
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="flex border-b">
          {[
            { id: 'upgrade', label: '浏览器升级' },
            { id: 'compatibility', label: '兼容性状态' },
            { id: 'troubleshooting', label: '故障排除' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'upgrade' && <UpgradeTab />}
          {activeTab === 'compatibility' && <CompatibilityTab />}
          {activeTab === 'troubleshooting' && <TroubleshootingTab />}
        </div>

        {/* 底部 */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompatibilityHelpModal;
