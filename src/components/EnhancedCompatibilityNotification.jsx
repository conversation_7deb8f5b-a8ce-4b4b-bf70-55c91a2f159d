/**
 * 增强版兼容性通知组件
 * 提供更好的用户体验和清晰的指导信息
 */

import React, { useState, useEffect } from 'react';
import { getEarlyCompatibilityResult } from '../utils/earlyCompatibilityCheck';
import { getRecommendedBrowsers } from '../utils/browserCompatibility';
import { useAnimationFallback } from '../hooks/useAnimationFallback';

const EnhancedCompatibilityNotification = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [notificationType, setNotificationType] = useState('info'); // 'error', 'warning', 'info'
  const [compatibilityResult, setCompatibilityResult] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [userDismissed, setUserDismissed] = useState(false);
  
  const { isAnimationDisabled, toggleFallback } = useAnimationFallback();

  // 检查兼容性状态
  useEffect(() => {
    const checkCompatibility = () => {
      const result = getEarlyCompatibilityResult();
      if (!result || userDismissed) return;

      setCompatibilityResult(result);

      // 根据兼容性结果决定显示类型
      if (!result.isBasicCompatible) {
        setNotificationType('error');
        setIsVisible(true);
      } else if (!result.isAnimationCompatible) {
        setNotificationType('warning');
        setIsVisible(true);
      } else if (result.browser && result.browser.version < 80) {
        setNotificationType('info');
        setIsVisible(true);
      }
    };

    checkCompatibility();

    // 监听兼容性检测事件
    const handleCompatibilityEvent = (event) => {
      const result = event.detail;
      setCompatibilityResult(result);
      checkCompatibility();
    };

    window.addEventListener('earlyCompatibilityChecked', handleCompatibilityEvent);
    window.addEventListener('browserCompatibilityChecked', handleCompatibilityEvent);

    return () => {
      window.removeEventListener('earlyCompatibilityChecked', handleCompatibilityEvent);
      window.removeEventListener('browserCompatibilityChecked', handleCompatibilityEvent);
    };
  }, [userDismissed]);

  // 获取通知样式
  const getNotificationStyles = () => {
    const baseStyles = "fixed top-4 right-4 max-w-md z-50 rounded-lg shadow-lg border transition-all duration-300";
    
    switch (notificationType) {
      case 'error':
        return `${baseStyles} bg-red-50 border-red-200 text-red-800`;
      case 'warning':
        return `${baseStyles} bg-yellow-50 border-yellow-200 text-yellow-800`;
      case 'info':
        return `${baseStyles} bg-blue-50 border-blue-200 text-blue-800`;
      default:
        return `${baseStyles} bg-gray-50 border-gray-200 text-gray-800`;
    }
  };

  // 获取图标
  const getIcon = () => {
    switch (notificationType) {
      case 'error':
        return '⚠️';
      case 'warning':
        return '⚡';
      case 'info':
        return 'ℹ️';
      default:
        return '💡';
    }
  };

  // 获取主要消息
  const getMainMessage = () => {
    if (!compatibilityResult) return '';

    const browserName = compatibilityResult.browser?.name || '当前浏览器';
    const browserVersion = compatibilityResult.browser?.version || '';

    switch (notificationType) {
      case 'error':
        return `${browserName} ${browserVersion} 可能无法正常运行本应用`;
      case 'warning':
        return `${browserName} ${browserVersion} 已启用兼容模式`;
      case 'info':
        return `建议升级 ${browserName} 以获得最佳体验`;
      default:
        return '浏览器兼容性提示';
    }
  };

  // 获取详细说明
  const getDetailMessage = () => {
    if (!compatibilityResult) return '';

    switch (notificationType) {
      case 'error':
        return '检测到您的浏览器版本过旧，可能无法支持本应用的核心功能。强烈建议升级到最新版本的现代浏览器。';
      case 'warning':
        return '您的浏览器不完全支持动画效果，已自动启用兼容模式。核心功能仍可正常使用，但视觉效果会有所简化。';
      case 'info':
        return '您的浏览器版本较旧，升级到最新版本可以获得更好的性能和完整的功能体验。';
      default:
        return '';
    }
  };

  // 获取推荐浏览器
  const getRecommendedBrowsersList = () => {
    const browsers = getRecommendedBrowsers();
    return browsers.slice(0, 4); // 只显示前4个推荐浏览器
  };

  // 处理关闭
  const handleDismiss = () => {
    setIsVisible(false);
    setUserDismissed(true);
    
    // 5分钟后允许再次显示
    setTimeout(() => {
      setUserDismissed(false);
    }, 5 * 60 * 1000);
  };

  // 处理切换动画模式
  const handleToggleAnimations = () => {
    toggleFallback();
  };

  if (!isVisible || !compatibilityResult) {
    return null;
  }

  return (
    <div className={getNotificationStyles()}>
      <div className="p-4">
        {/* 头部 */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <span className="text-lg">{getIcon()}</span>
            <h3 className="font-semibold text-sm">{getMainMessage()}</h3>
          </div>
          <button
            onClick={handleDismiss}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="关闭通知"
          >
            ×
          </button>
        </div>

        {/* 详细信息 */}
        <p className="text-xs mb-3 opacity-90">
          {getDetailMessage()}
        </p>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-2 mb-3">
          {notificationType === 'warning' && (
            <button
              onClick={handleToggleAnimations}
              className="px-3 py-1 text-xs bg-white border border-current rounded hover:bg-gray-50 transition-colors"
            >
              {isAnimationDisabled ? '启用动画' : '禁用动画'}
            </button>
          )}
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-1 text-xs bg-white border border-current rounded hover:bg-gray-50 transition-colors"
          >
            {showDetails ? '隐藏详情' : '查看详情'}
          </button>
        </div>

        {/* 详细信息展开 */}
        {showDetails && (
          <div className="border-t pt-3 mt-3">
            {/* 浏览器信息 */}
            <div className="mb-3">
              <h4 className="font-medium text-xs mb-1">当前浏览器信息</h4>
              <p className="text-xs opacity-75">
                {compatibilityResult.browser?.name || '未知'} {compatibilityResult.browser?.fullVersion || compatibilityResult.browser?.version || ''}
                {compatibilityResult.browser?.platform && ` (${compatibilityResult.browser.platform})`}
              </p>
            </div>

            {/* 推荐浏览器 */}
            <div className="mb-3">
              <h4 className="font-medium text-xs mb-2">推荐浏览器</h4>
              <div className="grid grid-cols-2 gap-2">
                {getRecommendedBrowsersList().map((browser, index) => (
                  <a
                    key={index}
                    href={browser.downloadUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 p-2 bg-white border border-current rounded text-xs hover:bg-gray-50 transition-colors"
                  >
                    <span>{browser.icon}</span>
                    <span>{browser.name}</span>
                  </a>
                ))}
              </div>
            </div>

            {/* 功能状态 */}
            {compatibilityResult.features && (
              <div>
                <h4 className="font-medium text-xs mb-1">功能支持状态</h4>
                <div className="text-xs opacity-75">
                  <div className="flex justify-between">
                    <span>基础功能:</span>
                    <span className={compatibilityResult.isBasicCompatible ? 'text-green-600' : 'text-red-600'}>
                      {compatibilityResult.isBasicCompatible ? '✓ 支持' : '✗ 不支持'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>动画效果:</span>
                    <span className={compatibilityResult.isAnimationCompatible ? 'text-green-600' : 'text-yellow-600'}>
                      {compatibilityResult.isAnimationCompatible ? '✓ 支持' : '⚡ 兼容模式'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedCompatibilityNotification;
